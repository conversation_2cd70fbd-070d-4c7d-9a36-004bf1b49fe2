"""Script de automatización para el formulario de registro de GitHub.

Flujo implementado:
1. Genera un perfil aleatorio (usuario, email, password) con generate_profile().
2. Abre un navegador endurecido usando Cam<PERSON>fox (fingerprint aleatorio) con resolución controlada.
3. Navega a https://github.com/signup y rellena los campos email, password y username.
4. Intenta enviar el formulario automáticamente con varias técnicas robustas.
5. Deja el navegador abierto para interacción manual posterior (captcha / octocaptcha / validaciones dinámicas).

Mejoras respecto a la versión previa:
- Se agregan múltiples selectores alternativos para el botón "Create account".
- Se valida visibilidad y estado (enabled) antes del click.
- Se fuerza blur/tab para que GitHub ejecute validaciones progresivas que habilitan/crean el botón.
- Nuevo intento: click humano por coordenadas aleatorias dentro del bounding box del botón antes de force/JS.
- Fallback: click JavaScript directo y click con force=True.
- Logs detallados para depurar por qué no se detecta el click.
- Corrige parámetro os: se usa un único valor (windows) si la librería no acepta lista.

Si aún no se ve efecto:
- Confirmar en la consola si aparece algún log de "NO visible" o "NO enabled".
- Posible bloqueo por octocaptcha o throttle de GitHub.

Toda la documentación permanece integrada como docstring.
"""

from utils import generate_profile
from camoufox import Camoufox
from browserforge.fingerprints import Screen
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from shared_state import get_signup_state, SignupStatus, log_state_change
import random
import time
import datetime
import threading
# ---------------------------
# Selectores centralizados
# ---------------------------
SELECTORS = {
    "email": "#email",
    "password": "#password",
    "username": "#login",
    # Conjunto de candidatos al botón para mayor resiliencia
    "primary_button_candidates": [
        "button[data-target='signup-form.SignupButton']",
        "button.js-octocaptcha-load-captcha.signup-form-fields__button",
        "button.js-octocaptcha-load-captcha",
        "button.signup-form-fields__button.Button--primary",
        "form button.Button--primary",
        "text=Create account",
    ],
    # Botón hidden de submit real (puede aparecer luego)
    "hidden_submit": "button.js-octocaptcha-form-submit:not([hidden])",
}

# Estructura global para reutilizar bounding box y coordenadas del último click exitoso por coordenadas.
LAST_CLICK_INFO = {
    "bbox": None,        # dict devuelto por locator.bounding_box()
    "coords": None,      # (x, y) usados en el último click por coordenadas
    "selector": None,    # descriptor / selector del elemento
    "timestamp": None,   # epoch del guardado
    "intentos": 0        # cuántos reintentos se han hecho reutilizando esta zona
}


def log(msg: str):
    """Helper simple de logging con timestamp preciso."""
    timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] [signup] {msg}")

def log_dom_state(page, description=""):
    """Registra el estado actual del DOM para debugging."""
    try:
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        url = page.url
        title = page.title()
        log(f"DOM_STATE {description} - URL: {url} - Title: {title}")
        
        # Verificar si hay errores en la consola
        try:
            errors = page.evaluate("""
                () => {
                    const errors = [];
                    const originalError = console.error;
                    console.error = function(...args) {
                        errors.push(args.join(' '));
                        originalError.apply(console, arguments);
                    };
                    return window.consoleErrors || [];
                }
            """)
            if errors:
                log(f"DOM_STATE {description} - Console errors: {errors}")
        except:
            pass
            
        # Verificar si hay elementos de formulario
        try:
            form_elements = page.evaluate("""
                () => {
                    const forms = document.querySelectorAll('form');
                    const inputs = document.querySelectorAll('input');
                    const buttons = document.querySelectorAll('button');
                    return {
                        forms: forms.length,
                        inputs: inputs.length,
                        buttons: buttons.length
                    };
                }
            """)
            log(f"DOM_STATE {description} - Forms: {form_elements['forms']}, Inputs: {form_elements['inputs']}, Buttons: {form_elements['buttons']}")
        except:
            pass
            
    except Exception as e:
        log(f"Error logging DOM state: {e}")


def rellenar_formulario(page, profile):
    """Rellena los campos principales del formulario de GitHub y dispara validaciones."""
    log("=== INICIANDO RELLENADO DE FORMULARIO ===")
    log_dom_state(page, "ANTES_DE_RELLENAR")
    
    # Rellenar email
    log(f"Rellenando email: {profile['email']}")
    start_time = time.time()
    page.fill(SELECTORS["email"], profile["email"])
    log(f"Email rellenado en {(time.time() - start_time)*1000:.1f}ms")
    time.sleep(0.2)  # Pequeña pausa entre campos
    
    # Rellenar password
    log(f"Rellenando password: {'*' * len(profile['password'])}")
    start_time = time.time()
    page.fill(SELECTORS["password"], profile["password"])
    log(f"Password rellenado en {(time.time() - start_time)*1000:.1f}ms")
    time.sleep(0.2)
    
    # Rellenar username
    log(f"Rellenando username: {profile['user']}")
    start_time = time.time()
    page.fill(SELECTORS["username"], profile["user"])
    log(f"Username rellenado en {(time.time() - start_time)*1000:.1f}ms")
    time.sleep(0.3)
    
    log_dom_state(page, "DESPUES_DE_RELLENAR")
    
    # Forzar eventos blur/tab para que GitHub active validaciones progresivas
    try:
        log("Presionando Tab tras username para disparar validaciones...")
        start_time = time.time()
        page.press(SELECTORS["username"], "Tab")
        elapsed = (time.time() - start_time) * 1000
        log(f"Tab presionado en {elapsed:.1f}ms - Esperando validaciones...")
        time.sleep(0.5)  # Esperar a que se procesen las validaciones
        log_dom_state(page, "DESPUES_DE_TAB")
    except Exception as e:
        log(f"ERROR: No se pudo hacer Tab tras username: {e}")
    
    log("=== FORMULARIO RELLENADO COMPLETAMENTE ===")


def _buscar_boton(page):
    """Devuelve el primer locator candidado que exista y loguea estado."""
    log("=== BÚSQUEDA DE BOTÓN INICIADA ===")
    log_dom_state(page, "ANTES_BUSCAR_BOTON")
    
    for i, sel in enumerate(SELECTORS["primary_button_candidates"]):
        log(f"Probando selector {i+1}/{len(SELECTORS['primary_button_candidates'])}: '{sel}'")
        start_time = time.time()
        
        locator = page.locator(sel)
        try:
            count = locator.count()
            search_time = (time.time() - start_time) * 1000
            log(f"  Búsqueda completada en {search_time:.1f}ms - Elementos encontrados: {count}")
        except Exception as e:
            search_time = (time.time() - start_time) * 1000
            log(f"  ERROR en búsqueda ({search_time:.1f}ms): {e}")
            count = 0
            
        if count > 0:
            # Elegimos primer elemento concreto
            candidate = locator.first
            
            # Verificar visibilidad
            try:
                start_time = time.time()
                visible = candidate.is_visible()
                visible_time = (time.time() - start_time) * 1000
                log(f"  Verificación de visibilidad: {visible} ({visible_time:.1f}ms)")
            except Exception as e:
                log(f"  ERROR verificando visibilidad: {e}")
                visible = False
                
            # Verificar si está habilitado
            try:
                start_time = time.time()
                enabled = candidate.is_enabled()
                enabled_time = (time.time() - start_time) * 1000
                log(f"  Verificación de enabled: {enabled} ({enabled_time:.1f}ms)")
            except Exception as e:
                log(f"  ERROR verificando enabled: {e}")
                enabled = False
                
            # Obtener información adicional del elemento
            try:
                bbox = candidate.bounding_box()
                if bbox:
                    log(f"  Bounding box: x={bbox['x']:.1f}, y={bbox['y']:.1f}, w={bbox['width']:.1f}, h={bbox['height']:.1f}")
                else:
                    log(f"  Bounding box: No disponible")
            except Exception as e:
                log(f"  ERROR obteniendo bounding box: {e}")
                
            # Obtener atributos del elemento
            try:
                attrs = candidate.evaluate("""
                    el => {
                        return {
                            tagName: el.tagName,
                            className: el.className,
                            id: el.id,
                            type: el.type,
                            disabled: el.disabled,
                            hidden: el.hidden,
                            style_display: getComputedStyle(el).display,
                            style_visibility: getComputedStyle(el).visibility
                        };
                    }
                """)
                log(f"  Atributos del elemento: {attrs}")
            except Exception as e:
                log(f"  ERROR obteniendo atributos: {e}")
            
            log(f"✓ BOTÓN ENCONTRADO - Selector: '{sel}' | Visible: {visible} | Enabled: {enabled}")
            return candidate, sel
        else:
            log(f"✗ Selector '{sel}' - No se encontraron elementos")
            
    log("✗ NO SE ENCONTRÓ NINGÚN BOTÓN CANDIDATO")
    return None, None


def _click_fuerza(page, locator, sel_desc):
    """Intentos escalonados de click.
    
    Orden:
    1. Click normal Playwright.
    2. Click coordenadas aleatorias dentro del bounding box (simula humano).
    3. Click force=True.
    4. Segundo intento coordenadas (si bbox).
    5. Click JS directo.
    
    Además:
    - Guarda en LAST_CLICK_INFO la bounding_box y coordenadas usadas.
    - Si no se consigue el click, deja la info para que un reintento posterior la reutilice.
    """
    global LAST_CLICK_INFO
    
    log(f"=== INICIANDO CLICK FORZADO EN '{sel_desc}' ===")
    log_dom_state(page, f"ANTES_CLICK_{sel_desc}")

    # 1. Click normal
    log("Intento 1/5: Click normal Playwright")
    start_time = time.time()
    try:
        locator.click(timeout=4000)
        elapsed = (time.time() - start_time) * 1000
        log(f"✓ Click normal EXITOSO ({sel_desc}) en {elapsed:.1f}ms")
        log_dom_state(page, f"DESPUES_CLICK_NORMAL_{sel_desc}")
        time.sleep(0.5)  # Pausa para ver el efecto
        return True
    except Exception as e:
        elapsed = (time.time() - start_time) * 1000
        log(f"✗ Click normal FALLÓ ({sel_desc}) en {elapsed:.1f}ms: {e}")
    
    # Intentar obtener bounding box solo una vez (puede fallar si elemento no layouted)
    bbox = None
    log("Obteniendo bounding box del elemento...")
    start_time = time.time()
    try:
        bbox = locator.bounding_box(timeout=2000)
        elapsed = (time.time() - start_time) * 1000
        if not bbox:
            log(f"✗ bounding_box vacío ({sel_desc}) en {elapsed:.1f}ms")
        else:
            log(f"✓ bounding_box obtenido ({sel_desc}) en {elapsed:.1f}ms: x={bbox['x']:.1f}, y={bbox['y']:.1f}, w={bbox['width']:.1f}, h={bbox['height']:.1f}")
            # Guardamos bbox base aunque todavía no haya click
            LAST_CLICK_INFO.update({
                "bbox": bbox,
                "coords": None,
                "selector": sel_desc,
                "timestamp": time.time(),
                "intentos": 0
            })
    except Exception as e:
        elapsed = (time.time() - start_time) * 1000
        log(f"✗ No se pudo obtener bounding_box ({sel_desc}) en {elapsed:.1f}ms: {e}")
    
    def _click_por_coordenadas(intent_label: str, attempt_num: int):
        """Genera un click dentro del rectángulo del elemento con jitter aleatorio."""
        log(f"Intento {attempt_num}/5: {intent_label}")
        if not bbox:
            log(f"✗ {intent_label} - No hay bounding box disponible")
            return False
            
        start_time = time.time()
        try:
            pad = 3
            w = max(1, bbox["width"])
            h = max(1, bbox["height"])
            if w <= pad * 2 or h <= pad * 2:
                pad = 0
            x = bbox["x"] + random.uniform(pad, w - pad)
            y = bbox["y"] + random.uniform(pad, h - pad)
            
            log(f"  Calculadas coordenadas: ({int(x)}, {int(y)}) con padding={pad}")
            
            # Mover antes de click para simular humano
            try:
                steps = random.randint(2, 5)
                log(f"  Moviendo mouse en {steps} pasos...")
                move_start = time.time()
                page.mouse.move(x, y, steps=steps)
                move_time = (time.time() - move_start) * 1000
                log(f"  Mouse movido en {move_time:.1f}ms")
            except Exception as move_e:
                log(f"  Advertencia: Error moviendo mouse: {move_e}")
                
            delay = random.randint(20, 120)
            log(f"  Haciendo click con delay de {delay}ms...")
            click_start = time.time()
            page.mouse.click(x, y, delay=delay)
            click_time = (time.time() - click_start) * 1000
            total_time = (time.time() - start_time) * 1000
            
            log(f"✓ {intent_label} EXITOSO ({sel_desc}) en ({int(x)},{int(y)}) - Click: {click_time:.1f}ms, Total: {total_time:.1f}ms")
            
            # Persistimos coordenadas usadas
            LAST_CLICK_INFO.update({
                "bbox": bbox,
                "coords": (x, y),
                "selector": sel_desc,
                "timestamp": time.time(),
                "intentos": 0
            })
            
            log_dom_state(page, f"DESPUES_{intent_label.replace(' ', '_')}_{sel_desc}")
            time.sleep(0.5)  # Pausa para ver el efecto
            return True
        except Exception as e_inner:
            elapsed = (time.time() - start_time) * 1000
            log(f"✗ {intent_label} FALLÓ ({sel_desc}) en {elapsed:.1f}ms: {e_inner}")
            return False
    
    # 2. Primer intento coordenadas
    if _click_por_coordenadas("Click coordenadas #1", 2):
        return True
    
    # 3. force=True
    log("Intento 3/5: Click force=True")
    start_time = time.time()
    try:
        locator.click(force=True, timeout=2000)
        elapsed = (time.time() - start_time) * 1000
        log(f"✓ Click force=True EXITOSO ({sel_desc}) en {elapsed:.1f}ms")
        log_dom_state(page, f"DESPUES_CLICK_FORCE_{sel_desc}")
        time.sleep(0.5)  # Pausa para ver el efecto
        return True
    except Exception as e:
        elapsed = (time.time() - start_time) * 1000
        log(f"✗ Click force=True FALLÓ ({sel_desc}) en {elapsed:.1f}ms: {e}")
    
    # 4. Segundo intento coordenadas (otra variación)
    if _click_por_coordenadas("Click coordenadas #2", 4):
        return True
    
    # 5. Click JS directo
    log("Intento 5/5: Click JavaScript directo")
    start_time = time.time()
    try:
        page.evaluate("(el)=>el.click()", locator)
        elapsed = (time.time() - start_time) * 1000
        log(f"✓ Click JS directo EXITOSO ({sel_desc}) en {elapsed:.1f}ms")
        log_dom_state(page, f"DESPUES_CLICK_JS_{sel_desc}")
        time.sleep(0.5)  # Pausa para ver el efecto
        return True
    except Exception as e:
        elapsed = (time.time() - start_time) * 1000
        log(f"✗ Click JS directo FALLÓ ({sel_desc}) en {elapsed:.1f}ms: {e}")
    
    log(f"✗ TODOS LOS INTENTOS DE CLICK FALLARON PARA '{sel_desc}'")
    return False


def reintentar_click_guardado(page, jitter=5):
    """Reintenta un click reutilizando la última zona almacenada en LAST_CLICK_INFO.
    
    Aplica un pequeño jitter aleatorio alrededor de las coordenadas previas para simular variación humana.
    Devuelve True si el click fue ejecutado sin excepción aparente.
    """
    global LAST_CLICK_INFO
    info = LAST_CLICK_INFO
    if not info.get("coords") or not info.get("bbox"):
        return False
    (base_x, base_y) = info["coords"]
    try:
        # Aplica jitter limitado al tamaño del bounding box para no salir del área.
        bbox = info["bbox"]
        max_jitter_x = min(jitter, max(1, bbox["width"] / 4))
        max_jitter_y = min(jitter, max(1, bbox["height"] / 4))
        x = base_x + random.uniform(-max_jitter_x, max_jitter_x)
        y = base_y + random.uniform(-max_jitter_y, max_jitter_y)
        try:
            page.mouse.move(x, y, steps=random.randint(1, 3))
        except Exception:
            pass
        delay = random.randint(15, 90)
        page.mouse.click(x, y, delay=delay)
        info["intentos"] = (info.get("intentos") or 0) + 1
        info["timestamp"] = time.time()
        log(f"Reintento coordenadas almacenadas OK ({info.get('selector')}) en ({int(x)},{int(y)}) intento#{info['intentos']}.")
        return True
    except Exception as e:
        log(f"Reintento coordenadas almacenadas falló: {e}")
        return False


def intentar_enviar(page):
    """Intenta avanzar en el flujo de registro.

    Estrategia escalonada:
    1. Enter en username (para flujos donde Enter dispara la progresión).
    2. Localizar botón con varios selectores candidatos (visibilidad + enabled).
    3. Intentar click con varias modalidades (normal, force, JS).
    4. Intentar botón hidden de submit si aparece (posible fase posterior).
    """
    log("=" * 60)
    log("=== INICIANDO INTENTO DE ENVÍO DE FORMULARIO ===")
    log("=" * 60)
    log_dom_state(page, "INICIO_INTENTAR_ENVIAR")
    
    avanzado = False

    # 1. Enter en el último campo
    log("PASO 1: Intentando Enter en username")
    start_time = time.time()
    try:
        page.press(SELECTORS["username"], "Enter")
        elapsed = (time.time() - start_time) * 1000
        log(f"✓ Enter en username ejecutado en {elapsed:.1f}ms")
        log_dom_state(page, "DESPUES_ENTER_USERNAME")
    except Exception as e:
        elapsed = (time.time() - start_time) * 1000
        log(f"✗ No se pudo enviar con Enter en {elapsed:.1f}ms: {e}")

    # Pequeña espera para ver si aparece algo nuevo
    log("Esperando 800ms para que se procesen cambios...")
    page.wait_for_timeout(800)
    log_dom_state(page, "DESPUES_ESPERA_800MS")

    # 2. Intentar localizar y click botón visible
    log("PASO 2: Localizando botón de envío")
    boton, sel = _buscar_boton(page)
    if boton:
        log(f"✓ Botón encontrado con selector: '{sel}'")
        
        # Aseguramos scroll
        log("Haciendo scroll al botón...")
        start_time = time.time()
        try:
            boton.scroll_into_view_if_needed(timeout=2000)
            elapsed = (time.time() - start_time) * 1000
            log(f"✓ Scroll completado en {elapsed:.1f}ms")
        except Exception as scroll_e:
            elapsed = (time.time() - start_time) * 1000
            log(f"⚠ Error en scroll ({elapsed:.1f}ms): {scroll_e}")
            
        # Espera de visibilidad (suave)
        log("Esperando que el botón sea visible...")
        start_time = time.time()
        try:
            boton.wait_for(state="visible", timeout=2500)
            elapsed = (time.time() - start_time) * 1000
            log(f"✓ Botón visible después de {elapsed:.1f}ms")
        except Exception as e:
            elapsed = (time.time() - start_time) * 1000
            log(f"⚠ El botón no alcanzó estado visible en {elapsed:.1f}ms: {e}")

        log_dom_state(page, "ANTES_CLICK_BOTON_PRINCIPAL")
        if _click_fuerza(page, boton, sel):
            log("✓ CLICK EN BOTÓN PRINCIPAL EXITOSO")
            avanzado = True
            log_dom_state(page, "DESPUES_CLICK_BOTON_EXITOSO")
        else:
            log("✗ CLICK EN BOTÓN PRINCIPAL FALLÓ")
            log_dom_state(page, "DESPUES_CLICK_BOTON_FALLIDO")
    else:
        log("✗ No se encontró ningún botón candidato para 'Create account' en esta etapa.")

    # 3. Intento sobre el botón hidden (si se vuelve visible)
    log("PASO 3: Verificando botón hidden submit")
    if avanzado:
        log("Como el click anterior fue exitoso, verificando botón hidden...")
        hidden = page.locator(SELECTORS["hidden_submit"])
        start_time = time.time()
        try:
            hidden_count = hidden.count()
            elapsed = (time.time() - start_time) * 1000
            log(f"Botones hidden encontrados: {hidden_count} (búsqueda en {elapsed:.1f}ms)")
            
            if hidden_count > 0:
                log("✓ Botón hidden submit detectado, intentando click...")
                log_dom_state(page, "ANTES_CLICK_HIDDEN")
                _click_fuerza(page, hidden.first, "hidden_submit")
                log_dom_state(page, "DESPUES_CLICK_HIDDEN")
            else:
                log("No hay botones hidden submit disponibles")
        except Exception as e:
            elapsed = (time.time() - start_time) * 1000
            log(f"✗ Error verificando hidden submit en {elapsed:.1f}ms: {e}")
    else:
        log("Saltando verificación de hidden submit (click principal falló)")

    # 4. Reintento reutilizando coordenadas almacenadas si no avanzó
    log("PASO 4: Verificando si necesita reintento con coordenadas guardadas")
    if not avanzado:
        log("Como no se logró avanzar, intentando con coordenadas guardadas...")
        start_time = time.time()
        try:
            log_dom_state(page, "ANTES_REINTENTO_COORDENADAS")
            if reintentar_click_guardado(page):
                elapsed = (time.time() - start_time) * 1000
                log(f"✓ Reintento con coordenadas guardadas EXITOSO en {elapsed:.1f}ms")
                avanzado = True
                log_dom_state(page, "DESPUES_REINTENTO_EXITOSO")
            else:
                elapsed = (time.time() - start_time) * 1000
                log(f"✗ Reintento con coordenadas guardadas FALLÓ en {elapsed:.1f}ms")
        except Exception as e:
            elapsed = (time.time() - start_time) * 1000
            log(f"✗ Error en reintentar_click_guardado en {elapsed:.1f}ms: {e}")
    else:
        log("No es necesario reintentar (ya se avanzó)")

    log("=" * 60)
    if avanzado:
        log("=== RESULTADO: ENVÍO EXITOSO ===")
    else:
        log("=== RESULTADO: ENVÍO FALLÓ ===")
    log("=" * 60)
    log_dom_state(page, "FINAL_INTENTAR_ENVIAR")
    
    return avanzado
def detectar_pagina_verificacion(page):
    """
    Detecta si estamos en la página de verificación de GitHub (launch code).
    Actualiza el estado compartido cuando se detecta.
    Implementa manejo thread-safe y delays apropiados.
    """
    state = get_signup_state()
    
    try:
        # Añadir delay antes de acceder a la página para evitar conflictos de thread
        time.sleep(0.5)
        
        current_url = page.url
        log(f"Verificando URL actual: {current_url}")
        
        # Verificar si estamos en la página de signup de GitHub
        if "github.com/signup" in current_url:
            # Buscar los campos del launch code con timeout más largo
            launch_code_selectors = [
                "input[id^='launch-code-']",
                "input[name='launch_code[]']",
                ".LaunchCode-module__input",
                "input[type='number'][maxlength='1']"
            ]
            
            for selector in launch_code_selectors:
                try:
                    # Añadir timeout y manejo más robusto
                    elements = page.locator(selector)
                    
                    # Usar wait_for con timeout reducido para evitar bloqueos
                    try:
                        elements.first.wait_for(state="attached", timeout=1000)
                    except:
                        pass  # Continuar aunque no esté attached
                    
                    count = elements.count()
                    if count >= 8:  # Debe haber 8 campos
                        log(f"✓ Página de verificación detectada - {count} campos encontrados con selector: {selector}")
                        state.update_status(
                            SignupStatus.VERIFICATION_PAGE,
                            current_url=current_url
                        )
                        log_state_change("Página de verificación detectada")
                        return True
                except Exception as e:
                    log(f"Error verificando selector {selector}: {e}")
                    # Añadir pequeño delay entre intentos de selector
                    time.sleep(0.2)
                    continue
    except Exception as e:
        log(f"Error detectando página de verificación: {e}")
    
    return False


def llenar_launch_code(page, codigo):
    """
    Llena los 8 campos del launch code con el código proporcionado.
    Implementa delays más largos y manejo thread-safe.
    
    Args:
        page: Página de Playwright
        codigo: String de 8 dígitos
    
    Returns:
        bool: True si se llenó exitosamente
    """
    if not codigo or len(codigo) < 8:
        log(f"✗ Código inválido para launch code: {codigo}")
        return False
    
    # Asegurar que tenemos exactamente 8 dígitos
    codigo_limpio = ''.join(filter(str.isdigit, codigo))[:8]
    if len(codigo_limpio) < 8:
        log(f"✗ Código no tiene suficientes dígitos: {codigo_limpio}")
        return False
    
    log(f"Llenando launch code con: {codigo_limpio}")
    
    # Delay inicial para estabilizar la página
    time.sleep(1)
    
    # Intentar diferentes selectores para los campos
    selectors_a_probar = [
        "input[id^='launch-code-']",
        "input[name='launch_code[]']",
        ".LaunchCode-module__input--eESr_",
        "input[type='number'][maxlength='1']"
    ]
    
    for selector_base in selectors_a_probar:
        try:
            elementos = page.locator(selector_base)
            
            # Esperar a que los elementos estén disponibles
            try:
                elementos.first.wait_for(state="visible", timeout=3000)
            except:
                log(f"Elementos con selector {selector_base} no están visibles")
                continue
            
            count = elementos.count()
            log(f"Selector {selector_base}: {count} elementos encontrados")
            
            if count >= 8:
                log(f"✓ Usando selector: {selector_base}")
                
                # Llenar cada campo individual con delays más largos
                for i in range(8):
                    try:
                        campo = elementos.nth(i)
                        digito = codigo_limpio[i]
                        
                        # Esperar a que el campo esté listo
                        try:
                            campo.wait_for(state="visible", timeout=1000)
                        except:
                            pass
                        
                        # Limpiar campo primero
                        campo.clear()
                        time.sleep(0.3)  # Aumentado de 0.1 a 0.3
                        
                        # Llenar con el dígito
                        campo.fill(digito)
                        time.sleep(0.3)  # Aumentado de 0.1 a 0.3
                        
                        # Opcional: simular Tab para avanzar
                        if i < 7:  # No hacer Tab en el último campo
                            try:
                                campo.press("Tab")
                                time.sleep(0.2)  # Delay adicional después del Tab
                            except:
                                pass
                        
                        log(f"  Campo {i}: '{digito}' ✓")
                        
                    except Exception as e:
                        log(f"✗ Error llenando campo {i}: {e}")
                        time.sleep(0.5)  # Delay adicional en caso de error
                        return False
                
                # Verificar que se llenaron correctamente con delay más largo
                time.sleep(1.5)  # Aumentado de 0.5 a 1.5
                valores_verificacion = []
                for i in range(8):
                    try:
                        valor = elementos.nth(i).input_value()
                        valores_verificacion.append(valor)
                    except:
                        valores_verificacion.append("?")
                
                codigo_verificacion = ''.join(valores_verificacion)
                log(f"Verificación - Código llenado: {codigo_verificacion}")
                
                if codigo_verificacion == codigo_limpio:
                    log("✓ Launch code llenado exitosamente")
                    state = get_signup_state()
                    state.update_status(SignupStatus.CODE_FILLED)
                    log_state_change("Launch code llenado exitosamente")
                    return True
                else:
                    log(f"✗ Verificación falló. Esperado: {codigo_limpio}, Obtenido: {codigo_verificacion}")
                    
        except Exception as e:
            log(f"Error con selector {selector_base}: {e}")
            time.sleep(0.5)  # Delay entre intentos de selector
            continue
    
    log("✗ No se pudo llenar el launch code con ningún selector")
    return False


def monitorear_url_y_codigo(page):
    """
    Función que monitorea cambios de URL y llena el código cuando está disponible.
    Se ejecuta en un hilo separado con delays más largos para evitar conflictos.
    """
    state = get_signup_state()
    log("Iniciando monitoreo de URL y código...")
    
    # Delay inicial para permitir que el proceso principal se estabilice
    time.sleep(3)
    
    while state.status not in [SignupStatus.COMPLETED, SignupStatus.ERROR]:
        try:
            # Verificar si estamos en página de verificación con delay más largo
            if not detectar_pagina_verificacion(page):
                log("No estamos en página de verificación, esperando 8 segundos...")
                time.sleep(8)  # Aumentado de 2 a 8 segundos
                continue
            
            # Si estamos en página de verificación y tenemos código, llenar
            if state.is_ready_for_code_filling():
                log(f"Estado listo para llenar código: {state.verification_code}")
                
                # Delay antes de intentar llenar para darle tiempo al DOM
                time.sleep(2)
                
                if llenar_launch_code(page, state.verification_code):
                    log("✓ Proceso de llenado completado")
                    state.update_status(SignupStatus.COMPLETED)
                    log_state_change("Proceso completado exitosamente")
                    break
                else:
                    log("✗ Error llenando el código")
                    state.update_status(SignupStatus.ERROR, error_message="Error llenando launch code")
                    break
            else:
                # Esperar a que llegue el código con delay más largo
                log("Esperando código de verificación...")
                time.sleep(10)  # Aumentado de 5 a 10 segundos
                
        except Exception as e:
            log(f"Error en monitoreo: {e}")
            # Delay más largo en caso de error para evitar spam
            time.sleep(5)  # Aumentado de 2 a 5 segundos
    
    log("Monitoreo terminado")




def main():
    log("=" * 80)
    log("=== INICIANDO AUTOMATIZACIÓN DE REGISTRO EN GITHUB ===")
    log("=" * 80)
    
    start_time_total = time.time()
    state = get_signup_state()
    
    # Generar perfil con datos únicos
    log("FASE 1: Generando perfil único")
    profile_start = time.time()
    profile = generate_profile()
    profile_time = (time.time() - profile_start) * 1000
    
    log(f"✓ Perfil generado en {profile_time:.1f}ms:")
    log(f"  Usuario : {profile['user']}")
    log(f"  Email   : {profile['email']}")
    log(f"  Password: {'*' * len(profile['password'])} ({len(profile['password'])} caracteres)")
    
    # Actualizar estado con información del perfil
    state.update_status(
        SignupStatus.INITIAL,
        email_used=profile['email'],
        username_used=profile['user']
    )
    log_state_change("Perfil generado")

    # Configurar Camoufox (mantener parámetros compatibles con la lib)
    log("FASE 2: Iniciando navegador Camoufox")
    browser_start = time.time()
    
    with Camoufox(
        os=["windows", "macos", "linux"],  # OS aleatorio
        fingerprint=None,  # Fingerprint aleatorio
        window=(1280, 720),  # Fuerza dimensiones exactas
        headless=False  # Asegura modo visual
    ) as browser:
        
        browser_time = (time.time() - browser_start) * 1000
        log(f"✓ Navegador iniciado en {browser_time:.1f}ms")

        log("FASE 3: Cargando página de registro")
        page_start = time.time()
        page = browser.new_page()
        page.goto("https://github.com/signup")
        page_time = (time.time() - page_start) * 1000
        log(f"✓ Página de signup cargada en {page_time:.1f}ms")
        log_dom_state(page, "PAGINA_INICIAL_CARGADA")
        
        # Actualizar URL inicial
        state.update_status(state.status, current_url=page.url)

        # Completar campos
        log("FASE 4: Rellenando formulario")
        form_start = time.time()
        rellenar_formulario(page, profile)
        form_time = (time.time() - form_start) * 1000
        log(f"✓ Formulario rellenado en {form_time:.1f}ms")
        
        state.update_status(SignupStatus.FORM_FILLED)
        log_state_change("Formulario rellenado")

        # Iniciar monitoreo en hilo separado
        log("FASE 5: Iniciando monitoreo de URL y código")
        monitor_thread = threading.Thread(
            target=monitorear_url_y_codigo,
            args=(page,),
            daemon=True
        )
        monitor_thread.start()

        # Intentar avanzar
        log("FASE 6: Intentando enviar formulario")
        submit_start = time.time()
        envio_exitoso = intentar_enviar(page)
        submit_time = (time.time() - submit_start) * 1000
        
        if envio_exitoso:
            log(f"✓ ENVÍO EXITOSO - Completado en {submit_time:.1f}ms")
            log_dom_state(page, "DESPUES_ENVIO_EXITOSO")
            state.update_status(SignupStatus.FORM_SUBMITTED, current_url=page.url)
            log_state_change("Formulario enviado exitosamente")
        else:
            log(f"✗ ENVÍO FALLÓ - Intentos completados en {submit_time:.1f}ms")
            log_dom_state(page, "DESPUES_ENVIO_FALLIDO")

        total_time = (time.time() - start_time_total) * 1000
        log("=" * 80)
        log(f"=== PROCESO INICIAL COMPLETADO EN {total_time:.1f}ms ===")
        log(f"  - Generación de perfil: {profile_time:.1f}ms")
        log(f"  - Inicio de navegador: {browser_time:.1f}ms")
        log(f"  - Carga de página: {page_time:.1f}ms")
        log(f"  - Rellenado de formulario: {form_time:.1f}ms")
        log(f"  - Intento de envío: {submit_time:.1f}ms")
        log("=" * 80)
        
        if envio_exitoso:
            log("🎉 REGISTRO INICIAL COMPLETADO")
            log("⏳ El navegador permanecerá abierto para el proceso de verificación automática")
            log("📧 Esperando código de verificación del email...")
            
            # Esperar hasta que el proceso se complete o haya error
            log("Esperando finalización del proceso automático (máximo 15 minutos)...")
            max_wait = 900  # 15 minutos
            check_interval = 10  # Verificar cada 10 segundos
            elapsed = 0
            
            while elapsed < max_wait and state.status not in [SignupStatus.COMPLETED, SignupStatus.ERROR]:
                time.sleep(check_interval)
                elapsed += check_interval
                
                # Log periódico del estado
                if elapsed % 60 == 0:  # Cada minuto
                    summary = state.get_state_summary()
                    log(f"Estado actual ({elapsed//60}min): {summary['status']}")
                    if summary['has_code']:
                        log(f"  Código disponible: {state.verification_code}")
            
            # Resultado final
            final_summary = state.get_state_summary()
            if state.status == SignupStatus.COMPLETED:
                log("🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
                log("✅ El código de verificación fue extraído y llenado automáticamente")
            elif state.status == SignupStatus.ERROR:
                log("❌ Error en el proceso automático")
                log(f"   Error: {final_summary.get('error_message', 'Desconocido')}")
            else:
                log("⏰ Timeout - Proceso no completado automáticamente")
                log("📧 Verifica manualmente el email y completa el proceso")
        else:
            log("⚠️ REGISTRO REQUIERE INTERVENCIÓN MANUAL")
            
        log("El navegador queda abierto para interacción manual si es necesario.")
        input("Presiona Enter para cerrar el navegador...")


if __name__ == "__main__":
    main()
