"""
Orquestador principal para el proceso completo de registro en GitHub.

Este módulo coordina:
1. El proceso de signup en GitHub (github_signup.py)
2. La extracción automática del código desde Hostinger Mail (hostinger_mail.py) 
3. El llenado automático del launch code
4. El monitoreo del estado compartido

Flujo completo:
- Inicia el proceso de signup de GitHub en un hilo
- Inicia la verificación de email en otro hilo
- Monitorea el estado compartido para coordinar las acciones
- Maneja errores y timeouts de forma centralizada
"""

import threading
import time
import sys
from typing import Optional

from shared_state import (
    get_signup_state, 
    SignupStatus, 
    log_state_change, 
    reset_signup_state,
    wait_for_verification_code,
    wait_for_verification_page
)
from hostinger_mail import run_github_verification_flow_background
import github_signup


class GitHubOrchestrator:
    """
    Orquestador principal que coordina el proceso completo de registro.
    """
    
    def __init__(self):
        self.state = get_signup_state()
        self.signup_thread: Optional[threading.Thread] = None
        self.email_thread: Optional[threading.Thread] = None
        self.running = False
        
    def log(self, message: str):
        """Helper de logging con timestamp."""
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        print(f"[{timestamp}] [ORCHESTRATOR] {message}")
    
    def start_signup_process(self):
        """Inicia el proceso de signup de GitHub en un hilo separado."""
        def signup_wrapper():
            try:
                self.log("Iniciando proceso de signup de GitHub...")
                github_signup.main()
            except Exception as e:
                self.log(f"Error en proceso de signup: {e}")
                self.state.update_status(SignupStatus.ERROR, error_message=f"Error signup: {e}")
        
        self.signup_thread = threading.Thread(target=signup_wrapper, daemon=True)
        self.signup_thread.start()
        self.log("Hilo de signup iniciado")
    
    def start_email_verification(self):
        """Inicia el proceso de verificación de email en un hilo separado."""
        def email_wrapper():
            try:
                # Esperar un poco para que el signup se envíe
                time.sleep(30)  # 30 segundos para que llegue el email
                self.log("Iniciando proceso de verificación de email...")
                run_github_verification_flow_background()
            except Exception as e:
                self.log(f"Error en verificación de email: {e}")
                self.state.update_status(SignupStatus.ERROR, error_message=f"Error email: {e}")
        
        self.email_thread = threading.Thread(target=email_wrapper, daemon=True)
        self.email_thread.start()
        self.log("Hilo de verificación de email iniciado")
    
    def monitor_process(self, max_duration: int = 1200):  # 20 minutos
        """
        Monitorea el progreso del proceso completo.
        
        Args:
            max_duration: Duración máxima en segundos (default: 20 minutos)
        """
        start_time = time.time()
        last_status = None
        
        self.log(f"Iniciando monitoreo por {max_duration//60} minutos...")
        
        while self.running and (time.time() - start_time) < max_duration:
            current_status = self.state.status
            
            # Log cambios de estado
            if current_status != last_status:
                summary = self.state.get_state_summary()
                self.log(f"Cambio de estado: {last_status} -> {current_status}")
                self.log(f"  URL actual: {summary.get('current_url', 'N/A')}")
                self.log(f"  Tiene código: {summary.get('has_code', False)}")
                last_status = current_status
            
            # Verificar condiciones de finalización
            if current_status == SignupStatus.COMPLETED:
                self.log("🎉 ¡PROCESO COMPLETADO EXITOSAMENTE!")
                self.log(f"✅ Código usado: {self.state.verification_code}")
                break
            elif current_status == SignupStatus.ERROR:
                self.log("❌ PROCESO TERMINADO CON ERROR")
                self.log(f"   Error: {self.state.error_message}")
                break
            
            # Log periódico de progreso
            elapsed = time.time() - start_time
            if int(elapsed) % 120 == 0 and int(elapsed) > 0:  # Cada 2 minutos
                self.log(f"Progreso ({int(elapsed//60)}min): {current_status.value}")
                if self.state.verification_code:
                    self.log(f"  Código disponible: {self.state.verification_code}")
            
            time.sleep(5)  # Verificar cada 5 segundos
        
        # Timeout
        if self.running and (time.time() - start_time) >= max_duration:
            self.log(f"⏰ TIMEOUT después de {max_duration//60} minutos")
            self.log(f"   Estado final: {self.state.status.value}")
            self.state.update_status(SignupStatus.ERROR, error_message="Timeout del orquestador")
    
    def run_complete_process(self):
        """
        Ejecuta el proceso completo de registro coordinado.
        """
        try:
            self.log("=" * 60)
            self.log("🚀 INICIANDO ORQUESTACIÓN COMPLETA DE REGISTRO GITHUB")
            self.log("=" * 60)
            
            # Reiniciar estado
            reset_signup_state()
            self.running = True
            
            # Fase 1: Iniciar proceso de signup
            self.log("FASE 1: Iniciando proceso de signup")
            self.start_signup_process()
            
            # Esperar a que el formulario se envíe
            self.log("Esperando que el formulario se envíe...")
            timeout_form = 300  # 5 minutos para llenar y enviar formulario
            start_wait = time.time()
            
            while (time.time() - start_wait) < timeout_form:
                if self.state.status in [SignupStatus.FORM_SUBMITTED, SignupStatus.VERIFICATION_PAGE]:
                    self.log("✅ Formulario enviado, continuando...")
                    break
                elif self.state.status == SignupStatus.ERROR:
                    self.log("❌ Error en fase de signup")
                    return
                time.sleep(2)
            else:
                self.log("⏰ Timeout esperando envío de formulario")
                self.state.update_status(SignupStatus.ERROR, error_message="Timeout envío formulario")
                return
            
            # Fase 2: Iniciar verificación de email
            self.log("FASE 2: Iniciando verificación de email")
            self.start_email_verification()
            
            # Fase 3: Monitoreo coordinado
            self.log("FASE 3: Monitoreo coordinado del proceso")
            self.monitor_process()
            
            # Resultado final
            final_summary = self.state.get_state_summary()
            self.log("=" * 60)
            self.log("📊 RESUMEN FINAL")
            self.log("=" * 60)
            self.log(f"Estado final: {final_summary['status']}")
            self.log(f"Email usado: {final_summary.get('email_used', 'N/A')}")
            self.log(f"Usuario: {final_summary.get('username_used', 'N/A')}")
            
            if final_summary['has_code']:
                self.log(f"Código extraído: {self.state.verification_code}")
            
            if final_summary.get('error_message'):
                self.log(f"Error: {final_summary['error_message']}")
            
            if self.state.status == SignupStatus.COMPLETED:
                self.log("🎉 ¡REGISTRO COMPLETADO EXITOSAMENTE!")
            else:
                self.log("⚠️  Proceso incompleto o con errores")
                
        except KeyboardInterrupt:
            self.log("⛔ Interrupción manual del proceso")
            self.state.update_status(SignupStatus.ERROR, error_message="Interrupción manual")
        except Exception as e:
            self.log(f"❌ Error inesperado en orquestador: {e}")
            self.state.update_status(SignupStatus.ERROR, error_message=f"Error orquestador: {e}")
        finally:
            self.running = False
            self.log("🔒 Orquestador finalizado")


def main():
    """Función principal para ejecutar el orquestador."""
    orchestrator = GitHubOrchestrator()
    orchestrator.run_complete_process()


if __name__ == "__main__":
    main()