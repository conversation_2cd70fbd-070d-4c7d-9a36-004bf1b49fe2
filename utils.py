import random
import string

# Listas para generar usuarios coherentes (ampliadas para más variedad)
adjectives = ['my', 'hella', 'super', 'awesome', 'cool', 'great', 'fantastic', 'samy', 'elaine', 'the', 'happy', 'sad', 'fast', 'slow', 'big', 'small', 'red', 'blue', 'green', 'yellow', 'smart', 'dumb', 'rich', 'poor', 'young', 'old', 'brave', 'shy', 'loud', 'quiet']
nouns = ['god', 'star', 'cat', 'dog', 'hero', 'wizard', 'ninja', 'good', 'lo', 'fundicion', 'bird', 'fish', 'tree', 'car', 'house', 'book', 'phone', 'computer', 'sun', 'moon', 'river', 'mountain', 'city', 'country', 'friend', 'enemy', 'dream', 'night', 'day', 'world']

# Dominios disponibles
domains = ['egonzalezdev.site', 'molinoyfundicionsm.com', 'cronistaurbano.com']

def generate_random_user(max_len=30):
    """Genera un nombre de usuario válido para GitHub (política solicitada).

    Restricciones implementadas:
    - Solo letras y números (se eliminan '_' y '-'; tampoco se añaden).
    - Longitud máxima configurable (default 30, < 39 límite GitHub).
    - Formato: adjetivo + sustantivo + número.
    - Sanitiza cualquier carácter inesperado (robustez futura).

    Si la cadena supera max_len se recorta preservando el bloque numérico final.
    """
    adj = random.choice(adjectives)
    noun = random.choice(nouns)
    number = str(random.randint(1, 9999))
    raw = f"{adj}{noun}{number}"

    # Mantener solo alfanuméricos
    alnum = ''.join(ch for ch in raw if ch.isalnum())

    if len(alnum) > max_len:
        num_len = len(number)
        base_len = max_len - num_len
        if base_len < 1:
            # Caso extremo: max_len demasiado pequeño para preservar todo número
            alnum = alnum[:max_len]
        else:
            alnum = alnum[:base_len] + number

    return alnum.lower()

def generate_random_password(length=12):
    """Genera una contraseña cumpliendo requisitos mínimos:
    
    Requisitos solicitados:
    - Al menos una letra minúscula.
    - Al menos un número.
    
    Estrategia:
    1. Seleccionar explícitamente 1 minúscula y 1 dígito.
    2. Completar el resto con el set completo (letras + dígitos + símbolos) para aleatoriedad.
    3. Barajar para no dejar siempre los primeros caracteres predecibles.
    
    Si length < 2 (caso extremo), se fuerza a length = 2 para poder cumplir las reglas.
    """
    if length < 2:
        length = 2
    lowercase = random.choice(string.ascii_lowercase)
    digit = random.choice(string.digits)
    chars = string.ascii_letters + string.digits + string.punctuation
    remaining = [random.choice(chars) for _ in range(length - 2)]
    pool = [lowercase, digit] + remaining
    random.shuffle(pool)
    return ''.join(pool)

def generate_random_email(user):
    """Genera un correo basado en el usuario y un dominio random."""
    domain = random.choice(domains)
    email = f"{user}@{domain}"
    return email

def generate_profile():
    """Genera un perfil completo con usuario, correo y password ligados."""
    user = generate_random_user()
    email = generate_random_email(user)
    password = generate_random_password()
    return {
        'user': user,
        'email': email,
        'password': password
    }

# Ejemplo de uso
if __name__ == "__main__":
    profile = generate_profile()
    print("Usuario:", profile['user'])
    print("Correo:", profile['email'])
    print("Password:", profile['password'])
