"""
Módulo de estado compartido para el flujo de registro de GitHub.

Proporciona variables globales compartidas entre el proceso de signup,
la extracción de código de email y el llenado del launch code.
"""

import threading
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from enum import Enum


class SignupStatus(Enum):
    """Estados del proceso de registro."""
    INITIAL = "initial"
    FORM_FILLED = "form_filled"
    FORM_SUBMITTED = "form_submitted"
    VERIFICATION_PAGE = "verification_page"
    CODE_EXTRACTED = "code_extracted"
    CODE_FILLED = "code_filled"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class SharedSignupState:
    """Estado compartido del proceso de registro."""
    status: SignupStatus = SignupStatus.INITIAL
    verification_code: Optional[str] = None
    current_url: Optional[str] = None
    email_used: Optional[str] = None
    username_used: Optional[str] = None
    error_message: Optional[str] = None
    last_updated: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_status(self, new_status: SignupStatus, **kwargs):
        """Actualiza el estado y timestamp de forma thread-safe."""
        with _state_lock:
            self.status = new_status
            self.last_updated = time.time()
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                else:
                    self.metadata[key] = value
    
    def set_verification_code(self, code: str):
        """Establece el código de verificación de forma thread-safe."""
        with _state_lock:
            self.verification_code = code
            self.update_status(SignupStatus.CODE_EXTRACTED)
    
    def is_ready_for_code_filling(self) -> bool:
        """Verifica si estamos listos para llenar el código."""
        with _state_lock:
            return (
                self.status == SignupStatus.VERIFICATION_PAGE and 
                self.verification_code is not None and
                self.current_url and 
                "github.com/signup" in self.current_url
            )
    
    def get_state_summary(self) -> Dict[str, Any]:
        """Retorna un resumen del estado actual."""
        with _state_lock:
            return {
                "status": self.status.value,
                "has_code": self.verification_code is not None,
                "current_url": self.current_url,
                "email_used": self.email_used,
                "username_used": self.username_used,
                "last_updated": self.last_updated,
                "error_message": self.error_message,
                "metadata": dict(self.metadata)
            }


# Variables globales compartidas
_state_lock = threading.Lock()
signup_state = SharedSignupState()


def get_signup_state() -> SharedSignupState:
    """Retorna la instancia del estado compartido."""
    return signup_state


def reset_signup_state():
    """Reinicia el estado compartido."""
    global signup_state
    with _state_lock:
        signup_state = SharedSignupState()


def log_state_change(message: str):
    """Log helper para cambios de estado."""
    timestamp = time.strftime("%H:%M:%S", time.localtime())
    state_summary = signup_state.get_state_summary()
    print(f"[{timestamp}] [STATE] {message}")
    print(f"  Estado actual: {state_summary['status']}")
    if state_summary['has_code']:
        print(f"  Código disponible: {signup_state.verification_code}")
    if state_summary['current_url']:
        print(f"  URL actual: {state_summary['current_url']}")


# Context manager para operaciones atómicas
class StateTransaction:
    """Context manager para operaciones atómicas en el estado."""
    
    def __enter__(self):
        _state_lock.acquire()
        return signup_state
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            signup_state.last_updated = time.time()
        _state_lock.release()


def wait_for_verification_code(timeout: float = 600) -> Optional[str]:
    """
    Espera hasta que esté disponible el código de verificación.
    
    Args:
        timeout: Tiempo máximo de espera en segundos (default: 10 minutos)
    
    Returns:
        El código de verificación o None si timeout
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        with _state_lock:
            if signup_state.verification_code:
                return signup_state.verification_code
        time.sleep(1)  # Check every second
    return None


def wait_for_verification_page(timeout: float = 300) -> bool:
    """
    Espera hasta que estemos en la página de verificación.
    
    Args:
        timeout: Tiempo máximo de espera en segundos (default: 5 minutos)
    
    Returns:
        True si llegamos a la página de verificación, False si timeout
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        with _state_lock:
            if (signup_state.status == SignupStatus.VERIFICATION_PAGE and 
                signup_state.current_url and 
                "github.com/signup" in signup_state.current_url):
                return True
        time.sleep(2)  # Check every 2 seconds
    return False