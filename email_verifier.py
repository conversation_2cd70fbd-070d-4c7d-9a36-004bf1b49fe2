# Approved by the leader
"""
Módulo: email_verifier
Clase principal EmailVerifier para manejar verificación y extracción de códigos
de correos electrónicos (p.ej. códigos de verificación de GitHub) desde
Hostinger Mail usando Camoufox / Playwright-like API.

NOTAS IMPORTANTES:
- No genera documentación externa; toda la documentación vive en docstrings.
- Diseñado para ser extensible a otros proveedores y tipos de correos.
- Seguro ante fallos: no lanza excepciones no controladas al exterior salvo
  errores críticos de inicialización / autenticación.
"""

from __future__ import annotations

import os
import re
import time
import json
import pickle
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable, Pattern, Tuple

try:
    from camoufox import Camoufox
    from camoufox.fingerprints import generate_fingerprint
    from browserforge.fingerprints import Fingerprint
except ImportError:
    Camoufox = None  # type: ignore
    generate_fingerprint = None  # type: ignore
    Fingerprint = Any  # type: ignore


# ===================================================================
# Excepciones Específicas
# ===================================================================

class EmailVerifierError(Exception):
    """Excepción base para errores de EmailVerifier."""


class EmailVerifierInitError(EmailVerifierError):
    """Error durante inicialización del sistema."""


class AuthenticationError(EmailVerifierError):
    """Error durante autenticación."""


class EmailSearchTimeoutError(EmailVerifierError):
    """Timeout alcanzado en proceso de búsqueda."""


class ContentExtractionError(EmailVerifierError):
    """Error en extracción de contenido de correo."""


class ConfigurationError(EmailVerifierError):
    """Parámetros de configuración inválidos."""


# ===================================================================
# Data Classes de Configuración / Resultados
# ===================================================================

@dataclass
class EmailSearchResult:
    """Representa el resultado de una búsqueda de correo."""
    success: bool
    verification_code: Optional[str] = None
    email_content: Optional[str] = None
    found_in: Optional[str] = None  # 'inbox' | 'spam'
    extraction_time: Optional[float] = None
    error: Optional[str] = None
    message: Optional[str] = None
    search_attempts: int = 0
    elapsed_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el resultado a dict serializable."""
        return {
            "success": self.success,
            "data": {
                "verification_code": self.verification_code,
                "email_content": self.email_content,
                "found_in": self.found_in,
                "extraction_time": self.extraction_time,
            } if self.success else {},
            "error": self.error,
            "message": self.message,
            "metadata": {
                "search_attempts": self.search_attempts,
                "elapsed_time": self.elapsed_time,
                **self.metadata
            }
        }


@dataclass
class EmailTypeDefinition:
    """Definición extensible de un tipo de correo soportado."""
    name: str
    filter_rules: Dict[str, Any]
    extraction_patterns: Dict[str, str]


@dataclass
class MailProviderConfig:
    """
    Configuración para un proveedor de correo. Permite adaptar la clase
    a otros webmails sin reescribir lógica central.
    """
    base_url: str
    login_selectors: Dict[str, str]
    inbox_selectors: Dict[str, str]
    # Campos opcionales
    spam_selectors: List[str] = field(default_factory=lambda: [
        "a[href*='spam']",
        "a[href*='junk']",
        "li[class*='spam'] a",
        "li[class*='junk'] a",
        "#mailboxlist a[onclick*='spam']",
        "#mailboxlist a[onclick*='junk']"
    ])


# ===================================================================
# Clase Principal
# ===================================================================

class EmailVerifier:
    """
    Clase para manejo de verificación de correos (principalmente códigos)
    en Hostinger Mail (Roundcube basado) con soporte para:
    - Búsqueda de correos no leídos (icono: msgicon status unread)
    - Espera adaptativa hasta 10 minutos
    - Fallback a carpeta SPAM tras 5 minutos
    - Extracción de códigos mediante patrones configurables
    - Sistema de notificaciones externo (utils.notify)

    USO BÁSICO:
        verifier = EmailVerifier()
        verifier.initialize()
        verifier.authenticate()
        result = verifier.wait_for_email(
            email_filter={'sender': 'GitHub', 'subject_contains': '[GitHub]'}
        )
        if result.success:
            print(result.verification_code)
        verifier.close()
    """

    # Configuración de tiempos por defecto (segundos)
    DEFAULT_INBOX_TIMEOUT = 300      # 5 minutos primera fase
    DEFAULT_TOTAL_TIMEOUT = 600      # 10 minutos total
    SPAM_FALLBACK_DELAY = 300        # 5 minutos para activar fallback
    DEFAULT_POLLING_FAST = 5
    DEFAULT_POLLING_MEDIUM = 15
    DEFAULT_POLLING_SLOW = 30

    # Archivos por defecto
    DEFAULT_CREDENTIALS_FILE = "credentials.json"
    DEFAULT_FINGERPRINT_FILE = "consistent_fingerprint.pkl"

    def __init__(
        self,
        credentials_file: str = DEFAULT_CREDENTIALS_FILE,
        fingerprint_file: str = DEFAULT_FINGERPRINT_FILE,
        notification_handler: Optional[Callable[[Dict[str, Any]], None]] = None,
        provider_config: Optional[MailProviderConfig] = None
    ) -> None:
        """
        Inicializa el verificador (no abre navegador aún).

        Args:
            credentials_file: Ruta archivo credenciales JSON.
            fingerprint_file: Ruta archivo fingerprint persistente.
            notification_handler: Callback para notificaciones estructuradas.
            provider_config: Configuración específica de proveedor (override).

        NOTA: Se permite inyectar provider_config para soportar otros webmails.
        """
        self.credentials_file = credentials_file
        self.fingerprint_file = fingerprint_file
        self.notification_handler = notification_handler

        self.browser = None
        self.page = None
        self.credentials: List[Dict[str, str]] = []
        self.credential_in_use: Optional[Dict[str, str]] = None
        self.fingerprint: Optional[Fingerprint] = None
        self.is_authenticated = False

        # Timeouts
        self.inbox_timeout = self.DEFAULT_INBOX_TIMEOUT
        self.total_timeout = self.DEFAULT_TOTAL_TIMEOUT
        self.spam_fallback_delay = self.SPAM_FALLBACK_DELAY

        # Estadísticas
        self.search_attempts = 0
        self.last_search_time: Optional[float] = None
        self._polling_intervals_used: List[int] = []

        # Tipos de correos registrados
        self.email_types: Dict[str, EmailTypeDefinition] = {}
        self._register_builtin_email_types()

        # Config proveedor
        self.provider_config = provider_config or self._default_provider_config()

    # ------------------------------------------------------------------
    # Configuración y Registro
    # ------------------------------------------------------------------

    def _default_provider_config(self) -> MailProviderConfig:
        """Config por defecto para Hostinger Roundcube."""
        return MailProviderConfig(
            base_url="https://mail.hostinger.com/",
            login_selectors={
                "username_field": "#rcmloginuser",
                "password_field": "#rcmloginpwd",
                "submit_button": "#rcmloginsubmit"
            },
            inbox_selectors={
                "message_list": "#messagelist",
                "message_row": "tr.message",
                "subject_td": "td.subject",
                "iframe": "#messagecontframe"
            },
        )

    def _register_builtin_email_types(self) -> None:
        """Registra tipos de correo básicos integrados en el sistema."""
        self.register_email_type(
            email_type="github_verification",
            filter_rules={
                "sender": "GitHub",
                "subject_contains": "GitHub",
                "has_unread_status": True
            },
            extraction_patterns={
                "verification_code": r"Verification code:\s*(\d+)"
            }
        )

    def register_email_type(
        self,
        email_type: str,
        filter_rules: Dict[str, Any],
        extraction_patterns: Dict[str, str]
    ) -> None:
        """
        Registra un nuevo tipo de correo soportado.

        Args:
            email_type: Identificador del tipo (ej: 'github_verification').
            filter_rules: Reglas de filtrado (sender, subject_contains, etc).
            extraction_patterns: Patrones regex clave - valor.

        Raises:
            ConfigurationError: Si parámetros inválidos.
        """
        if not email_type or not isinstance(filter_rules, dict) or not extraction_patterns:
            raise ConfigurationError("Definición inválida de email_type")
        self.email_types[email_type] = EmailTypeDefinition(
            name=email_type,
            filter_rules=filter_rules,
            extraction_patterns=extraction_patterns
        )

    def register_mail_provider(self, provider_config: MailProviderConfig) -> None:
        """
        Registra (o reemplaza) la configuración de un proveedor de correo.

        Args:
            provider_config: Instancia MailProviderConfig.
        """
        self.provider_config = provider_config

    def configure_timeouts(
        self,
        inbox_timeout: Optional[int] = None,
        total_timeout: Optional[int] = None,
        spam_fallback_delay: Optional[int] = None
    ) -> None:
        """
        Configura timeouts personalizados.

        Validaciones:
        - total_timeout > inbox_timeout
        - spam_fallback_delay <= inbox_timeout

        Raises:
            ConfigurationError: Si parámetros inválidos.
        """
        if inbox_timeout is not None:
            if inbox_timeout <= 0:
                raise ConfigurationError("inbox_timeout debe ser > 0")
            self.inbox_timeout = inbox_timeout
        if total_timeout is not None:
            if total_timeout <= 0:
                raise ConfigurationError("total_timeout debe ser > 0")
            self.total_timeout = total_timeout
        if spam_fallback_delay is not None:
            if spam_fallback_delay < 0:
                raise ConfigurationError("spam_fallback_delay inválido")
            self.spam_fallback_delay = spam_fallback_delay

        if self.total_timeout <= self.inbox_timeout:
            raise ConfigurationError("total_timeout debe ser > inbox_timeout")
        if self.spam_fallback_delay > self.inbox_timeout:
            raise ConfigurationError("spam_fallback_delay debe ser <= inbox_timeout")

    def set_notification_handler(self, handler: Callable[[Dict[str, Any]], None]) -> None:
        """Inyecta un handler de notificaciones / logging."""
        self.notification_handler = handler

    # ------------------------------------------------------------------
    # Notificación Interna
    # ------------------------------------------------------------------

    def _notify(self, notification: Dict[str, Any]) -> None:
        """
        Envia notificación estructurada al handler si existe.
        Falla silenciosa si handler lanza excepción.
        """
        if self.notification_handler:
            try:
                self.notification_handler(notification)
            except Exception:
                pass  # No propagar

    def _notify_simple(self, type_: str, message: str, level: str = "info", data: Optional[Dict[str, Any]] = None):
        """Conveniencia de notificación simplificada."""
        payload = {
            "type": type_,
            "level": level,
            "message": message,
            "timestamp": time.time(),
            "data": data or {}
        }
        self._notify(payload)

    # ------------------------------------------------------------------
    # Inicialización / Autenticación
    # ------------------------------------------------------------------

    def initialize(self) -> bool:
        """
        Inicializa el navegador, fingerprint y credenciales.

        Returns:
            True si inicialización exitosa.

        Raises:
            EmailVerifierInitError: Fallo crítico en inicialización.
        """
        try:
            self._load_or_generate_fingerprint()
            self._load_credentials()

            if Camoufox is None:
                raise EmailVerifierInitError("Camoufox no está instalado o importable.")

            self.browser = Camoufox(
                fingerprint=self.fingerprint,
                i_know_what_im_doing=True
            )
            self.page = self.browser.new_page()
            self.page.goto(self.provider_config.base_url)
            self._notify_simple("BROWSER_INITIALIZED", "Navegador inicializado y página abierta", "success")
            return True
        except Exception as e:
            raise EmailVerifierInitError(f"Fallo en initialize(): {e}") from e

    def _load_or_generate_fingerprint(self) -> None:
        """Carga o genera fingerprint consistente."""
        if os.path.exists(self.fingerprint_file):
            try:
                with open(self.fingerprint_file, "rb") as f:
                    self.fingerprint = pickle.load(f)
                self._notify_simple("FINGERPRINT_LOADED", "Fingerprint cargado desde archivo", "info")
                return
            except Exception:
                try:
                    os.remove(self.fingerprint_file)
                except OSError:
                    pass
        if generate_fingerprint is None:
            raise EmailVerifierInitError("generate_fingerprint no disponible")
        fp = generate_fingerprint(os="windows")
        with open(self.fingerprint_file, "wb") as f:
            pickle.dump(fp, f)
        self.fingerprint = fp
        self._notify_simple("FINGERPRINT_GENERATED", "Nuevo fingerprint generado", "info")

    def _load_credentials(self) -> None:
        """Carga credenciales desde JSON."""
        if not os.path.exists(self.credentials_file):
            raise EmailVerifierInitError("Archivo de credenciales no encontrado")
        try:
            with open(self.credentials_file, "r", encoding="utf-8") as f:
                self.credentials = json.load(f)
            if not isinstance(self.credentials, list) or not self.credentials:
                raise EmailVerifierInitError("Formato de credenciales inválido o vacío")
            self._notify_simple("CREDENTIALS_LOADED", f"Credenciales cargadas: {len(self.credentials)}", "info")
        except Exception as e:
            raise EmailVerifierInitError(f"Error leyendo credenciales: {e}") from e

    def authenticate(self, preferred_domain: str = "egonzalezdev.site") -> bool:
        """
        Autentica contra Hostinger Mail.

        Args:
            preferred_domain: Dominio preferido para seleccionar credencial.

        Returns:
            True si autenticación exitosa.

        Raises:
            AuthenticationError: Fallo en login.
        """
        if not self.page:
            raise AuthenticationError("Página no inicializada. Llamar initialize() primero.")

        try:
            selectors = self.provider_config.login_selectors
            self.page.wait_for_selector(selectors["username_field"])
            cred = next((c for c in self.credentials if preferred_domain in c.get("email", "")), None)
            if not cred:
                cred = self.credentials[0]
            self.credential_in_use = cred

            self.page.fill(selectors["username_field"], cred["email"])
            self.page.fill(selectors["password_field"], cred["password"])
            self.page.click(selectors["submit_button"])

            self.page.wait_for_selector(self.provider_config.inbox_selectors["message_list"], timeout=15000)
            self.is_authenticated = True
            self._notify_simple("AUTHENTICATION_SUCCESS", f"Autenticado como {cred['email']}", "success")
            return True
        except Exception as e:
            raise AuthenticationError(f"Fallo autenticación: {e}") from e

    # ------------------------------------------------------------------
    # Búsqueda Principal de Correo
    # ------------------------------------------------------------------

    def wait_for_email(
        self,
        email_filter: Dict[str, Any],
        extraction_pattern: Optional[str] = None,
        email_type: Optional[str] = None
    ) -> EmailSearchResult:
        """
        Espera un correo que cumpla el filtro y extrae contenido/código.

        Args:
            email_filter: Dict criterios (sender, subject_contains, subject_regex, has_unread_status).
            extraction_pattern: Patrón regex directo (prioridad si se pasa).
            email_type: Nombre de tipo registrado para usar patrones predefinidos.

        Returns:
            EmailSearchResult estructurado.

        Raises:
            EmailSearchTimeoutError: Si se agota total_timeout.
        """
        if not self.is_authenticated:
            raise AuthenticationError("No autenticado. Llamar authenticate() antes de wait_for_email().")

        start_time = time.time()
        spam_started = False
        self.search_attempts = 0
        self._polling_intervals_used.clear()

        # Derivar patrón de verificación si no viene explicitado
        if not extraction_pattern and email_type and email_type in self.email_types:
            extraction_pattern = self.email_types[email_type].extraction_patterns.get("verification_code")

        self._notify_simple("EMAIL_SEARCH_START", f"Iniciando búsqueda con filtro: {email_filter}", "info")

        while True:
            elapsed = time.time() - start_time
            if elapsed >= self.total_timeout:
                result = EmailSearchResult(
                    success=False,
                    error="TIMEOUT",
                    message=f"No se encontró correo tras {self.total_timeout} segundos",
                    search_attempts=self.search_attempts,
                    elapsed_time=elapsed,
                    metadata={"polling_intervals": self._polling_intervals_used}
                )
                self._notify_simple("TIMEOUT", result.message, "error", result.to_dict())
                raise EmailSearchTimeoutError(result.message)

            # Determinar ubicación (inbox o spam)
            location = "spam" if elapsed >= self.spam_fallback_delay else "inbox"
            if location == "spam" and not spam_started:
                spam_started = True
                self._notify_simple("SPAM_FALLBACK", "Transicionando a carpeta SPAM", "warning")

                # Navegar a spam solo una vez
                self._navigate_to_spam_folder()

            # Búsqueda
            unread_items = self.search_unread_emails(location=location)
            matches = self._filter_emails(unread_items, email_filter)

            if matches:
                process_start = time.time()
                result = self._process_matching_email(matches[0], extraction_pattern, location)
                result.extraction_time = time.time() - process_start
                result.elapsed_time = elapsed
                result.search_attempts = self.search_attempts
                result.metadata["polling_intervals"] = self._polling_intervals_used
                if result.success:
                    self._notify_simple("EMAIL_FOUND", f"Correo encontrado en {location}", "success")
                    if result.verification_code:
                        self._notify_simple(
                            "VERIFICATION_CODE_EXTRACTED",
                            f"Código extraído: {result.verification_code}",
                            "success"
                        )
                    return result
                else:
                    # Continúa búsqueda si extracción falló
                    self._notify_simple("EXTRACTION_ERROR", result.message or "Fallo extracción", "error")

            # Calcular y esperar próximo intervalo
            interval = self._calculate_next_polling_interval(elapsed)
            self._polling_intervals_used.append(interval)
            time.sleep(interval)
            self.search_attempts += 1
            self.last_search_time = time.time()

    # ------------------------------------------------------------------
    # Búsqueda / Filtrado Bajo Nivel
    # ------------------------------------------------------------------

    def search_unread_emails(self, location: str = "inbox") -> List[Any]:
        """
        Busca filas de correo no leído (icono unread) en la carpeta actual.

        Args:
            location: 'inbox' o 'spam' (solo informativo / logging).

        Returns:
            Lista de elementos fila (DOM-like objects).
        """
        if not self.page:
            return []
        selectors = self.provider_config.inbox_selectors
        try:
            rows = self.page.query_selector_all(selectors["message_row"])
            unread_rows = []
            for row in rows:
                try:
                    subject_td = row.query_selector(selectors["subject_td"])
                    if not subject_td:
                        continue
                    unread_icon = subject_td.query_selector(
                        "[class*='msgicon'][class*='status'][class*='unread']"
                    )
                    if unread_icon:
                        unread_rows.append(row)
                except Exception:
                    continue
            return unread_rows
        except Exception:
            return []

    def _filter_emails(self, email_elements: List[Any], email_filter: Dict[str, Any]) -> List[Any]:
        """Filtra elementos que cumplan criterios de email_filter."""
        return [e for e in email_elements if self._email_matches_filter(e, email_filter)]

    def _email_matches_filter(self, email_element: Any, email_filter: Dict[str, Any]) -> bool:
        """
        Verifica criterios:
        - sender
        - subject_contains
        - subject_regex
        - has_unread_status (implícito en search_unread_emails ya)
        """
        try:
            # Sender
            if "sender" in email_filter:
                from_span = email_element.query_selector("span.rcmContactAddress")
                from_text = (from_span.inner_text() if from_span else "").lower()
                if email_filter["sender"].lower() not in from_text:
                    return False

            # Subject contains
            if "subject_contains" in email_filter:
                subject_span = email_element.query_selector("span.subject a span")
                subject_text = (subject_span.inner_text() if subject_span else "").lower()
                if email_filter["subject_contains"].lower() not in subject_text:
                    return False

            # Subject regex
            if "subject_regex" in email_filter:
                subject_span = email_element.query_selector("span.subject a span")
                subject_text = subject_span.inner_text() if subject_span else ""
                pattern = email_filter["subject_regex"]
                if not re.search(pattern, subject_text):
                    return False

            return True
        except Exception:
            return False

    # ------------------------------------------------------------------
    # Procesamiento de Correo
    # ------------------------------------------------------------------

    def _process_matching_email(
        self,
        email_element: Any,
        extraction_pattern: Optional[str],
        location: str
    ) -> EmailSearchResult:
        """
        Abre el correo, intenta cargar iframe y extraer texto/código.
        """
        selectors = self.provider_config.inbox_selectors
        try:
            link = email_element.query_selector("span.subject a")
            if link:
                link.click()
            else:
                return EmailSearchResult(
                    success=False,
                    error="NO_LINK",
                    message="No se encontró link para abrir el correo",
                    found_in=location
                )

            # Esperar iframe
            try:
                self.page.wait_for_selector(selectors["iframe"], timeout=10000)
            except Exception:
                # Fallback sin iframe
                page_content = self.page.content()
                return self._extract_from_raw_html(
                    page_content, extraction_pattern, location
                )

            iframe = self.page.frame(name="messagecontframe")
            if not iframe:
                iframe_element = self.page.query_selector(selectors["iframe"])
                if iframe_element:
                    iframe = iframe_element.content_frame()

            if not iframe:
                # Intentar extracción desde página principal
                page_content = self.page.content()
                return self._extract_from_raw_html(page_content, extraction_pattern, location)

            iframe.wait_for_timeout(1500)  # ligera espera para carga
            # Intentar múltiples selectores
            selectors_to_try = [
                "#message-part1",
                "#message-htmlpart1",
                ".message-part",
                "div[id*='message-part']",
                ".pre",
                "body"
            ]
            extracted_texts: List[str] = []
            for sel in selectors_to_try:
                try:
                    elements = iframe.query_selector_all(sel)
                    for elem in elements:
                        try:
                            html = elem.inner_html()
                            if html and len(html) > 20:
                                clean = re.sub(r"<[^>]+>", " ", html)
                                clean = re.sub(r"\s+", " ", clean).strip()
                                if clean:
                                    extracted_texts.append(clean)
                        except Exception:
                            continue
                    if extracted_texts:
                        break
                except Exception:
                    continue

            combined_text = " ".join(extracted_texts) if extracted_texts else ""
            return self._extract_from_raw_text(combined_text, extraction_pattern, location)

        except Exception as e:
            return EmailSearchResult(
                success=False,
                error="PROCESS_ERROR",
                message=f"Error procesando correo: {e}",
                found_in=location
            )

    def _extract_from_raw_html(self, html: str, extraction_pattern: Optional[str], location: str) -> EmailSearchResult:
        """Limpia HTML y delega a extracción de texto plano."""
        clean_iframe = re.sub(r"<[^>]+>", " ", html)
        clean_iframe = re.sub(r"\s+", " ", clean_iframe).strip()
        return self._extract_from_raw_text(clean_iframe, extraction_pattern, location)

    def _extract_from_raw_text(self, text: str, extraction_pattern: Optional[str], location: str) -> EmailSearchResult:
        """
        Ejecuta extracción del código de verificación usando patrón
        provisto o heurística por defecto.
        """
        if not text:
            return EmailSearchResult(
                success=False,
                error="EMPTY_CONTENT",
                message="Contenido vacío del correo",
                found_in=location
            )
        pattern = extraction_pattern or r"Verification code:\s*(\d+)"
        match = re.search(pattern, text)
        if match:
            code = match.group(1)
            return EmailSearchResult(
                success=True,
                verification_code=code,
                email_content=text,
                found_in=location
            )
        return EmailSearchResult(
            success=False,
            error="CODE_NOT_FOUND",
            message="No se encontró código de verificación",
            email_content=text,
            found_in=location
        )

    def extract_verification_code(self, email_element: Any, pattern: str = r"Verification code:\s*(\d+)") -> Optional[str]:
        """
        (Método auxiliar individual, normalmente no se llama directamente)
        Abre un correo y devuelve el código según patrón.
        """
        res = self._process_matching_email(email_element, pattern, location="inbox")
        return res.verification_code if res.success else None

    # ------------------------------------------------------------------
    # Navegación a SPAM
    # ------------------------------------------------------------------

    def _navigate_to_spam_folder(self) -> bool:
        """
        Intenta navegar a la carpeta spam con múltiples selectores.
        """
        if not self.page:
            return False
        for selector in self.provider_config.spam_selectors:
            try:
                el = self.page.query_selector(selector)
                if el:
                    el.click()
                    self.page.wait_for_selector(self.provider_config.inbox_selectors["message_list"], timeout=8000)
                    return True
            except Exception:
                continue
        # Fallback: intentar expandir listas
        try:
            folder_links = self.page.query_selector_all("#mailboxlist a")
            for link in folder_links:
                try:
                    txt = link.inner_text().lower()
                    if any(term in txt for term in ["spam", "junk", "no deseado"]):
                        link.click()
                        self.page.wait_for_selector(self.provider_config.inbox_selectors["message_list"], timeout=8000)
                        return True
                except Exception:
                    continue
        except Exception:
            pass
        return False

    # ------------------------------------------------------------------
    # Polling Interval Adaptativo
    # ------------------------------------------------------------------

    def _calculate_next_polling_interval(self, elapsed_time: float) -> int:
        """
        Estrategia:
        0-2 min  : 5s
        2-5 min  : 15s
        5-10 min : 30s
        """
        if elapsed_time < 120:
            return self.DEFAULT_POLLING_FAST
        elif elapsed_time < self.inbox_timeout:
            return self.DEFAULT_POLLING_MEDIUM
        else:
            return self.DEFAULT_POLLING_SLOW

    # ------------------------------------------------------------------
    # Limpieza
    # ------------------------------------------------------------------

    def close(self) -> None:
        """Cierra el navegador si existe."""
        try:
            if self.browser:
                self.browser.close()
        except Exception:
            pass
        finally:
            self.browser = None
            self.page = None
            self.is_authenticated = False

    # ------------------------------------------------------------------
    # Context Manager Support
    # ------------------------------------------------------------------

    def __enter__(self) -> "EmailVerifier":
        if not self.browser:
            self.initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        self.close()


# ===================================================================
# Ejemplo de uso (opcional, no ejecutar automáticamente en import)
# ===================================================================

if __name__ == "__main__":
    # Ejemplo manual (descomentar para pruebas locales)
    """
    def simple_print_handler(evt: Dict[str, Any]):
        print(f"[{evt['type']}] {evt['message']}")

    verifier = EmailVerifier(notification_handler=simple_print_handler)
    verifier.initialize()
    verifier.authenticate()
    try:
        res = verifier.wait_for_email(
            email_filter={"sender": "GitHub", "subject_contains": "GitHub"},
            email_type="github_verification"
        )
        print(res.to_dict())
    except EmailSearchTimeoutError as e:
        print("Timeout:", e)
    finally:
        verifier.close()
    """
    pass