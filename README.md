# Sistema de Registro Automático para GitHub

Sistema completo que automatiza el registro en GitHub, incluyendo la extracción automática del código de verificación desde el email y el llenado del launch code.

## Archivos Principales

### 🔧 Módulos Core
- **`shared_state.py`** - Estado compartido thread-safe entre componentes
- **`github_signup.py`** - Automatización del formulario de registro de GitHub
- **`hostinger_mail.py`** - Extracción de códigos desde Hostinger Mail
- **`email_verifier.py`** - Verificador de emails (existente)
- **`utils.py`** - Utilidades para generar perfiles (existente)

### 🎛️ Orquestación
- **`github_orchestrator.py`** - Coordinador principal del proceso completo

### 🧪 Pruebas
- **`test_integration.py`** - Tests completos de integración
- **`simple_test.py`** - Test básico de funcionalidad

## Flujo Completo

### 1. Proceso Coordinado
```bash
python github_orchestrator.py
```

El orquestador:
1. ✅ Inicia el proceso de signup de GitHub
2. ✅ Detecta cuando se llega a la página de verificación (`https://github.com/signup`)
3. ✅ Extrae automáticamente el código del email de Hostinger
4. ✅ Llena automáticamente los 8 campos del launch code
5. ✅ Monitorea el estado y maneja errores

### 2. Procesos Individuales

#### Signup Manual
```bash
python github_signup.py
```

#### Verificación de Email Manual
```bash
python hostinger_mail.py
```

## Estado Compartido

El sistema usa un estado global thread-safe que coordina:

- **Estado actual**: `INITIAL` → `FORM_FILLED` → `FORM_SUBMITTED` → `VERIFICATION_PAGE` → `CODE_EXTRACTED` → `CODE_FILLED` → `COMPLETED`
- **Código de verificación**: Compartido entre el extractor de email y el llenador de formulario
- **URL actual**: Para detectar la página de verificación
- **Información del perfil**: Email y username generados

## Componentes Clave

### Detección de Página de Verificación
```python
def detectar_pagina_verificacion(page):
    # Detecta cuando estamos en https://github.com/signup
    # Busca los 8 campos del launch code
    # Actualiza el estado compartido
```

### Llenado Automático del Launch Code
```python
def llenar_launch_code(page, codigo):
    # Llena los 8 campos: launch-code-0 hasta launch-code-7
    # Valida que el código tenga exactamente 8 dígitos
    # Verifica que se llenó correctamente
```

### Extracción de Código desde Email
```python
def run_github_verification_flow_background():
    # Extrae dominio del email generado para GitHub
    domain = state.email_used.split('@')[1]  # ej: "cronistaurbano.com"
    # Busca credenciales correspondientes en credentials.json
    verifier.authenticate(preferred_domain=domain)
    # Busca emails de GitHub no leídos en esa cuenta
    # Extrae el código usando regex: "Verification code:\s*(\d+)"
    # Actualiza el estado compartido automáticamente
```

## Estructura del Launch Code

El sistema maneja este HTML automáticamente:
```html
<div class="Primer_Brand__Box-module__Box-marginBlockStart--condensed___bTE8Q">
  <div class="Primer_Brand__Stack-module__Stack___tASKe">
    <!-- 8 campos de entrada para el código -->
    <input id="launch-code-0" class="LaunchCode-module__input--eESr_" 
           type="number" maxlength="1" name="launch_code[]" required="">
    <input id="launch-code-1" class="LaunchCode-module__input--eESr_" 
           type="number" maxlength="1" name="launch_code[]" required="">
    <!-- ... hasta launch-code-7 -->
  </div>
</div>
```

## Sistema de Dominios Correspondientes

**IMPORTANTE**: El sistema extrae el dominio del email generado y usa las credenciales correspondientes:

- ✅ **Email generado**: `<EMAIL>`
- ✅ **Credenciales usadas**: `<EMAIL>` (del `credentials.json`)
- ✅ **Coincidencia por dominio**: Extrae `cronistaurbano.com` y busca credenciales de ese dominio
- ✅ **Automático**: No necesita configuración manual por cada email

### Ejemplo de Flujo por Dominio:
1. GitHub signup genera: `<EMAIL>`
2. Sistema extrae dominio: `cronistaurbano.com`
3. Busca en `credentials.json`: `<EMAIL>`
4. ✅ **Entra con esas credenciales** para buscar el correo
5. Encuentra y extrae el código automáticamente

### Dominios Disponibles en `credentials.json`:
- `cronistaurbano.com` → `<EMAIL>`
- `molinoyfundicionsm.com` → `<EMAIL>`
- `egonzalezdev.site` → `<EMAIL>`

## Dependencias Necesarias

```bash
pip install camoufox playwright browserforge
```

Para el módulo de email:
- Credenciales en `credentials.json`
- Fingerprint en `consistent_fingerprint.pkl`

## Variables de Estado

### SignupStatus (Enum)
- `INITIAL` - Estado inicial
- `FORM_FILLED` - Formulario completado
- `FORM_SUBMITTED` - Formulario enviado
- `VERIFICATION_PAGE` - En página de verificación
- `CODE_EXTRACTED` - Código extraído del email
- `CODE_FILLED` - Código llenado en el formulario
- `COMPLETED` - Proceso completado
- `ERROR` - Error en el proceso

### Datos Compartidos
- `verification_code` - Código de 8 dígitos extraído
- `current_url` - URL actual del navegador
- `email_used` - Email del perfil generado
- `username_used` - Username del perfil generado
- `error_message` - Mensaje de error si aplica

## Uso Avanzado

### Configuración de Timeouts
```python
# En github_orchestrator.py
max_duration = 1200  # 20 minutos máximo
check_interval = 5   # Verificar cada 5 segundos
```

### Personalización de Selectores
```python
# En github_signup.py
launch_code_selectors = [
    "input[id^='launch-code-']",
    "input[name='launch_code[]']",
    ".LaunchCode-module__input--eESr_",
    "input[type='number'][maxlength='1']"
]
```

### Monitoreo del Estado
```python
from shared_state import get_signup_state
state = get_signup_state()
print(f"Estado: {state.status}")
print(f"Código: {state.verification_code}")
print(f"URL: {state.current_url}")
```

## Logs y Debugging

El sistema incluye logging detallado:
- Estados de transición
- Detección de páginas
- Extracción de códigos
- Errores y timeouts
- Coordenadas de clicks (para debugging)

## Seguridad Thread-Safe

Todas las operaciones en el estado compartido son thread-safe usando locks:
```python
with StateTransaction() as state:
    state.verification_code = "12345678"
    state.status = SignupStatus.CODE_EXTRACTED
```

## Contribuciones

Para agregar nuevos tipos de email o proveedores:
1. Extender `EmailVerifier` en `email_verifier.py`
2. Registrar nuevos patrones de extracción
3. Actualizar la configuración del proveedor

## Solución de Problemas

### Error: "No module named 'camoufox'"
```bash
pip install camoufox
```

### Error: "Credentials not found"
Asegurar que existe `credentials.json` con:
```json
[
  {
    "email": "<EMAIL>",
    "password": "tu-password"
  }
]
```

### Timeout en extracción de código
- Verificar que el email llegó a la bandeja
- Revisar carpeta de SPAM
- Aumentar timeout en configuración