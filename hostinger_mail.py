"""
Adaptación del flujo original para usar EmailVerifier.

- Se reemplaza la lógica imperativa anterior por el uso de la clase EmailVerifier
  definida en email_verifier.py
- Mantiene print básicos para compatibilidad y debug rápido.
- Evita duplicar lógica de fingerprint / credenciales (delegado a EmailVerifier).

Si se desea comportamiento más avanzado de notificaciones, implementar un
handler y pasarlo a EmailVerifier(notification_handler=handler).
"""

from email_verifier import (
    EmailVerifier,
    EmailSearchTimeoutError,
    AuthenticationError,
    EmailVerifierInitError
)
from shared_state import get_signup_state, SignupStatus, log_state_change
import threading


def simple_notification_handler(evt: dict):
    """
    Handler simple de notificaciones para canalizar eventos a consola.
    Este adaptador traduce la notificación estructurada a prints legibles.
    """
    level_icon = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }.get(evt.get("level"), "📌")
    print(f"{level_icon} [{evt.get('type')}] {evt.get('message')}")


def run_github_verification_flow():
    """
    Ejecuta el flujo de verificación de correo GitHub aprovechando EmailVerifier.
    Actualiza automáticamente el estado compartido cuando encuentra el código.
    """
    state = get_signup_state()
    verifier = EmailVerifier(notification_handler=simple_notification_handler)
    
    try:
        verifier.initialize()
        verifier.authenticate(preferred_domain="egonzalezdev.site")

        # Filtro para localizar correos de GitHub con status unread.
        email_filter = {
            "sender": "GitHub",
            "subject_contains": "GitHub",
            "has_unread_status": True
        }

        # Esperar correo con lógica de polling + fallback a spam.
        print("🔍 Iniciando espera de correo de verificación (hasta 10 minutos)...")
        result = verifier.wait_for_email(
            email_filter=email_filter,
            email_type="github_verification"  # Usa patrón registrado
        )

        if result.success and result.verification_code:
            print("🎉 Código de verificación encontrado:", result.verification_code)
            print(f"   Ubicación: {result.found_in}")
            print(f"   Intentos: {result.search_attempts}")
            print(f"   Tiempo transcurrido: {result.elapsed_time:.1f}s")
            
            # Actualizar estado compartido con el código
            state.set_verification_code(result.verification_code)
            log_state_change(f"Código de verificación extraído: {result.verification_code}")
        else:
            print("No se logró extraer el código. Detalles:")
            print(result.to_dict())
            state.update_status(SignupStatus.ERROR, error_message="No se pudo extraer código del email")

        # Mantener sesión abierta para inspección manual opcional
        input("Presiona Enter para cerrar el navegador...")

    except EmailSearchTimeoutError as e:
        print("⏰ Timeout de búsqueda:", e)
        state.update_status(SignupStatus.ERROR, error_message=f"Timeout buscando email: {e}")
    except AuthenticationError as e:
        print("❌ Error de autenticación:", e)
        state.update_status(SignupStatus.ERROR, error_message=f"Error de autenticación: {e}")
    except EmailVerifierInitError as e:
        print("❌ Error de inicialización:", e)
        state.update_status(SignupStatus.ERROR, error_message=f"Error de inicialización: {e}")
    except KeyboardInterrupt:
        print("⛔ Interrupción manual del flujo.")
        state.update_status(SignupStatus.ERROR, error_message="Interrupción manual")
    finally:
        verifier.close()
        print("🔒 Sesión cerrada.")


def run_github_verification_flow_background():
    """
    Versión para ejecutar en segundo plano que no requiere input del usuario.
    Diseñada para ser llamada desde el orquestador principal.
    
    IMPORTANTE: Extrae el dominio del email generado para GitHub y usa las
    credenciales correspondientes de ese dominio en credentials.json
    """
    state = get_signup_state()
    verifier = EmailVerifier(notification_handler=simple_notification_handler)
    
    try:
        print("📧 [BACKGROUND] Iniciando verificación de email en segundo plano...")
        print(f"📧 [BACKGROUND] Email GitHub generado: {state.email_used}")
        
        # Extraer dominio del email generado
        if state.email_used:
            domain = state.email_used.split('@')[1]
            print(f"📧 [BACKGROUND] Dominio extraído: {domain}")
            print(f"📧 [BACKGROUND] Buscando credenciales para dominio: {domain}")
        else:
            domain = "egonzalezdev.site"  # Fallback
            print("📧 [BACKGROUND] No hay email definido, usando dominio por defecto")
        
        verifier.initialize()
        # Usar credenciales del dominio correspondiente
        verifier.authenticate(preferred_domain=domain)

        # Filtro para localizar correos de GitHub con status unread
        # Busca cualquier correo de GitHub independientemente del destinatario
        email_filter = {
            "sender": "GitHub",
            "subject_contains": "GitHub",
            "has_unread_status": True
        }

        print("🔍 [BACKGROUND] Esperando correo de verificación...")
        print(f"🔍 [BACKGROUND] Buscando en cuenta del dominio: {domain}")
        
        result = verifier.wait_for_email(
            email_filter=email_filter,
            email_type="github_verification"  # Usa patrón registrado
        )

        if result.success and result.verification_code:
            print(f"🎉 [BACKGROUND] Código extraído: {result.verification_code}")
            print(f"🎉 [BACKGROUND] Email GitHub: {state.email_used}")
            print(f"🎉 [BACKGROUND] Cuenta usada: credenciales de {domain}")
            
            # Actualizar estado compartido con el código
            state.set_verification_code(result.verification_code)
            log_state_change(f"Código de verificación extraído automáticamente: {result.verification_code}")
        else:
            print("❌ [BACKGROUND] No se logró extraer el código")
            print(f"❌ [BACKGROUND] Verificar correo en cuenta de dominio: {domain}")
            state.update_status(SignupStatus.ERROR, error_message="No se pudo extraer código del email")

    except EmailSearchTimeoutError as e:
        print(f"⏰ [BACKGROUND] Timeout: {e}")
        print("⏰ [BACKGROUND] El correo de GitHub puede no haber llegado aún")
        state.update_status(SignupStatus.ERROR, error_message=f"Timeout buscando email: {e}")
    except AuthenticationError as e:
        print(f"❌ [BACKGROUND] Error de autenticación: {e}")
        state.update_status(SignupStatus.ERROR, error_message=f"Error de autenticación: {e}")
    except EmailVerifierInitError as e:
        print(f"❌ [BACKGROUND] Error de inicialización: {e}")
        state.update_status(SignupStatus.ERROR, error_message=f"Error de inicialización: {e}")
    except Exception as e:
        print(f"❌ [BACKGROUND] Error inesperado: {e}")
        state.update_status(SignupStatus.ERROR, error_message=f"Error inesperado: {e}")
    finally:
        verifier.close()
        print("🔒 [BACKGROUND] Sesión de email cerrada.")


if __name__ == "__main__":
    run_github_verification_flow()
